﻿/*********************************************************************************
// This code is created by SimCoder Version 2025a15.147 for F28004x Hardware Target
//
// SimCoder is copyright by Powersim Inc., 2009-2021
//
// Date: July 30, 2025 21:11:17
**********************************************************************************/
#include	<math.h>
#include	"PS_bios.h"
#define	GetCurTime() PS_GetSysTimer()
#define	iif(a, b, c) ((a) ? (b) : (c))
typedef interrupt void (*ClaIntr)(void);
ClaIntr Cla1Task1 = 0;
ClaIntr Cla1Task2 = 0;
ClaIntr Cla1Task3 = 0;
ClaIntr Cla1Task4 = 0;
ClaIntr Cla1Task5 = 0;
ClaIntr Cla1Task6 = 0;
ClaIntr Cla1Task7 = 0;
ClaIntr Cla1Task8 = 0;




interrupt void Task();
interrupt void Task_1();

#ifdef _FLASH
#pragma DATA_SECTION(PSK_SysClk, "copysections")
#endif
const Uint16 PSK_SysClk = 100;  // MHz
extern	DefaultType	fGblSa;
extern	DefaultType	fGblSab;
extern	DefaultType	fGblSbc;
extern	DefaultType	fGblSca;
extern	DefaultType	fGblVodc;
extern	DefaultType	fGblVod;
extern	DefaultType	fGblIod;
extern	DefaultType	fGblIoq;
extern	DefaultType	fGblVcona;
extern	DefaultType	fGblVconb;
extern	DefaultType	fGblVconc;
extern	DefaultType	fGblIodc;
extern	DefaultType	fGblIoqc;
extern	DefaultType	fGblUDELAY5;
extern	DefaultType	fGblSQ2;
extern	DefaultType	fGblUDELAY9;
extern	DefaultType	fGblUDELAY10;
extern	DefaultType	fGblSQ1;
extern	DefaultType	fGblUDELAY11;
extern	DefaultType	fGblUDELAY12;
extern	DefaultType	fGblSQ3;
extern	DefaultType	fGblUDELAY13;
extern	DefaultType	fGblMULT1;
extern	DefaultType	fGblMULT9;
extern	DefaultType	fGblMULT10;
extern	DefaultType	fGblMULT11;
extern	DefaultType	fGblP35;
extern	DefaultType	fGblP36;
extern	DefaultType	fGblP39;
extern	DefaultType	fGblP40;
extern	DefaultType	fGblP42;
extern	DefaultType	fGblP43;
extern	DefaultType	fGblP46;
extern	DefaultType	fGblP47;
extern	DefaultType	fGblP48;
extern	DefaultType	fGblV1rms;
extern	DefaultType	fGblV2rms;
extern	DefaultType	fGblV3rms;
extern	DefaultType	fGblUDELAY15;
extern	DefaultType	fGblUDELAY3;
extern	DefaultType	fGblUDELAY16;
extern	DefaultType	fGblUDELAY17;


#define	PSM_VRefHiA		3.3		// ADC-A VREFHIA
#define	PSM_VRefHiB		3.3		// ADC-B VREFHIB
#define	PSM_VRefHiC		3.3		// ADC-C VREFHIC





PST_BufItem aGblSciOutBuf[64];
Uint16 aGblSciOutAllow[24] = {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0};
Uint16 aGblSciOutCnt[24] = {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0};
Uint16 nGblSciState = 0;
Uint16 aGblSciDateSetPt[24] = {1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1};
char* aGblSciInitStr = "\0016,1:PSM_Voab=20000\003\0016,2:PSM_Vobc=20000\003\0016,3:PSM_Voca=20000\003\0016,4:PSM_Voa=20000\003\0016,5:PSM_Vob=20000\003\0016,6:PSM_Voc=20000\003\0016,7:PSM_ILa=20000\003\0016,8:PSM_ILb=20000\003\0016,9:PSM_Ioa=20000\003\0016,10:PSM_Ioc=20000\003\0016,11:PSM_Iod=20000\003\0016,12:PSM_Iodc=20000\003\0016,13:PSM_Ioq=20000\003\0016,14:PSM_Ioqc=20000\003\0016,15:PSM_Voq=20000\003\0016,16:PSM_Voqc=20000\003\0016,17:PSM_Vod=20000\003\0016,18:PSM_Vodc=20000\003\0016,19:Voab_rms=20000\003\0016,20:Vobc_rms=20000\003\0016,21:Voca_rms=20000\003\0016,22:PSM_Vcona=20000\003\0016,23:PSM_Vconb=20000\003\0016,24:PSM_Vconc=20000\003";
#define	PSC_SCI_SENDOUT_FLAG	0x2000
#define	PSC_SCI_INITIAL		0
#define	PSC_SCI_START		0x5000000
#define	PSC_SCI_PAUSE		0x1000000
#define	PSC_SCI_RESTART		0x2000000
#define	PSC_SCI_CONT_MODE	0x3000000
#define	PSC_SCI_SNAP_MODE	0x4000000
#define	PSC_SCI_CONT_START	1
#define	PSC_SCI_CONT_BEGIN	2
#define	PSC_SCI_CONT_SEND	3
#define	PSC_SCI_CONT_PAUSE	4
#define	PSC_SCI_SNAP_START	100
#define	PSC_SCI_SNAP_BEGIN	101
#define	PSC_SCI_SNAP_SEND	102
#define	PSC_SCI_SNAP_WAIT	103
#define	PSC_SCI_SNAP_PSEND	104
#define	PSC_SCI_SNAP_PWAIT	105
#define	PSC_SCI_SNAP_PAUSE	106
void _ProcSciInputItem(PST_BufItem* pItem)
{
	Uint16 i, nSeqNo = pItem->nSeqNo.bit.nSeqNo;
	switch (nSeqNo) {
	case 0:
		switch (pItem->data.dataInt32) {
		case PSC_SCI_INITIAL:
			for (i = 0; i < 24; i++) aGblSciOutAllow[i] = 0;
			PS_SciClearSendBuf();
			PS_SciSendInitStr(aGblSciInitStr);
			break;
		case PSC_SCI_PAUSE:
			PSM_DisableIntr();
			switch (nGblSciState) {
			case PSC_SCI_CONT_START:
			case PSC_SCI_CONT_SEND:
				PS_SciClearSendBuf();
				nGblSciState = PSC_SCI_CONT_PAUSE;
				break;
			case PSC_SCI_SNAP_SEND:
				nGblSciState = PSC_SCI_SNAP_PSEND;
				break;
			case PSC_SCI_SNAP_WAIT:
				nGblSciState = PSC_SCI_SNAP_PWAIT;
				break;
			default:
				break;
			}
			PSM_EnableIntr();
			break;
		case PSC_SCI_RESTART:
			PSM_DisableIntr();
			switch (nGblSciState) {
			case PSC_SCI_CONT_PAUSE:
				nGblSciState = PSC_SCI_CONT_START;
				break;
			case PSC_SCI_SNAP_PSEND:
			case PSC_SCI_SNAP_PWAIT:
			case PSC_SCI_SNAP_PAUSE:
				nGblSciState = PSC_SCI_SNAP_START;
				break;
			}
			PSM_EnableIntr();
			break;
		case PSC_SCI_CONT_MODE:
			nGblSciState = PSC_SCI_CONT_START;
			break;
		case PSC_SCI_SNAP_MODE:
			nGblSciState = PSC_SCI_SNAP_START;
			break;
		default:
			if (pItem->nSeqNo.bit.nCount == 0) {
				for (i = 0; i < 24; i++) aGblSciOutAllow[i] = 0;
			}
			for (i = 0; i < 4; i++) {
				int index = (pItem->data.dataInt32 >> (i * 8)) & 0xff;
				if ((index > 0) && (index <= 24))
					aGblSciOutAllow[index - 1] = PSC_SCI_SENDOUT_FLAG;
			}
			break;
		}
		break;
	}
}

void _ProcSciRestart(void)
{
	int i;
	PST_BufItem item;

	for (i = 0; i < 24; i++)
		aGblSciOutAllow[i] &= 0xff00;
	item.nSeqNo.all = 0;
	switch (nGblSciState++) {
	case PSC_SCI_CONT_BEGIN:
		PS_SciClearSendBuf();
		item.data.dataInt32 = 0;
		break;
	case PSC_SCI_SNAP_BEGIN:
		item.data.dataInt32 = 1;
		break;
	case PSC_SCI_SNAP_PWAIT:
		nGblSciState = PSC_SCI_SNAP_START;
	case PSC_SCI_SNAP_WAIT:
		item.data.dataInt32 = 255;
		break;
	}
	PS_SciSendItem(&item);
}

void _ProcSciWaitStart(void)
{
	PSM_DisableIntr();
	switch (nGblSciState) {
	case PSC_SCI_CONT_START:
		nGblSciState = PSC_SCI_CONT_BEGIN;
		break;
	case PSC_SCI_SNAP_START:
		nGblSciState = PSC_SCI_SNAP_BEGIN;
		break;
	default:
		break;
	}
	PSM_EnableIntr();
}

void _ProcSciOutput(int index, float fVal)
{
	PST_BufItem item;
	int ok = ((aGblSciOutAllow[index] & PSC_SCI_SENDOUT_FLAG) &&
		(++aGblSciOutCnt[index] >= aGblSciDateSetPt[index]));
	PSM_DisableIntr();
	switch (nGblSciState) {
	case PSC_SCI_CONT_BEGIN:
	case PSC_SCI_SNAP_BEGIN:
		_ProcSciRestart();
		break;
	case PSC_SCI_CONT_SEND:
		if (ok) {
			aGblSciOutCnt[index] = 0;
			item.nSeqNo.bit.nCount = aGblSciOutAllow[index];
			item.nSeqNo.bit.nSeqNo = index + 1;
			item.data.dataFloat = fVal;
			PS_SciSendItem(&item);
			aGblSciOutAllow[index]++;
			aGblSciOutAllow[index] &= ~0x100;
		}
		break;
	case PSC_SCI_SNAP_SEND:
	case PSC_SCI_SNAP_PSEND:
		if (ok) {
			aGblSciOutCnt[index] = 0;
			item.nSeqNo.bit.nCount = aGblSciOutAllow[index];
			item.nSeqNo.bit.nSeqNo = index + 1;
			item.data.dataFloat = fVal;
			if (!PS_SciSendItem(&item)) {
				nGblSciState++;
			} else {
				aGblSciOutAllow[index]++;
				aGblSciOutAllow[index] &= ~0x100;
			}
		}
		break;
	case PSC_SCI_SNAP_WAIT:
		if (PS_IsTxQueueEmpty()) {
			nGblSciState = PSC_SCI_SNAP_START;
		}
		break;
	case PSC_SCI_SNAP_PWAIT:
		if (PS_IsTxQueueEmpty()) {
			nGblSciState = PSC_SCI_SNAP_PAUSE;
		}
		break;
	default:
		break;
	}
	PSM_EnableIntr();
}

DefaultType	fGblSa = 0;
DefaultType	fGblSab = 0;
DefaultType	fGblSbc = 0;
DefaultType	fGblSca = 0;
DefaultType	fGblVodc = 0;
DefaultType	fGblVod = 0;
DefaultType	fGblIod = 0;
DefaultType	fGblIoq = 0;
DefaultType	fGblVcona = 0;
DefaultType	fGblVconb = 0;
DefaultType	fGblVconc = 0;
DefaultType	fGblIodc = 0;
DefaultType	fGblIoqc = 0;
int32	nGblC_BUFFER_12 = 0;
DefaultType	aryC_BUFFER_12[] = {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,0,0,0,0};
DefaultType	fGblUDELAY5 = 0;
DefaultType	fGblSQ2 = 0;
DefaultType	fGblUDELAY9 = 0;
int32	nGblC_BUFFER_11 = 0;
DefaultType	aryC_BUFFER_11[] = {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,0,0,0,0};
DefaultType	fGblUDELAY10 = 0;
DefaultType	fGblSQ1 = 0;
DefaultType	fGblUDELAY11 = 0;
int32	nGblC_BUFFER_13 = 0;
DefaultType	aryC_BUFFER_13[] = {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,0,0,0,0};
DefaultType	fGblUDELAY12 = 0;
DefaultType	fGblSQ3 = 0;
DefaultType	fGblUDELAY13 = 0;
DefaultType	fGblMULT1 = 0;
DefaultType	fGblMULT9 = 0;
DefaultType	fGblMULT10 = 0;
DefaultType	fGblMULT11 = 0;
DefaultType	fGblP35 = 0;
DefaultType	fGblP36 = 0;
DefaultType	fGblP39 = 0;
DefaultType	fGblP40 = 0;
DefaultType	fGblP42 = 0;
DefaultType	fGblP43 = 0;
DefaultType	fGblP46 = 0;
DefaultType	fGblP47 = 0;
DefaultType	fGblP48 = 0;
DefaultType	fGblV1rms = 0;
DefaultType	fGblV2rms = 0;
DefaultType	fGblV3rms = 0;
DefaultType	fGblUDELAY15 = 0;
DefaultType	fGblUDELAY3 = 0;
DefaultType	fGblUDELAY16 = 0;
DefaultType	fGblUDELAY17 = 0;
interrupt void Task()
{
	DefaultType	fNOT1, fLIM17, fLIM16, fLIM15, fZOH35, fZOH34, fZOH33, fZOH32;
	DefaultType	fZOH31, fZOH30, fZOH29, fZOH28, fZOH24, fZOH23, fZOH17, fZOH16;
	DefaultType	fZOH15, fZOH14, fZOH48, fZOH46, fZOH44, fZOH43, fZOH42, fZOH41;
	DefaultType	fZOH40, fZOH39, fZOH38, fZOH37, fUDELAY13, fMULT6, fUDELAY12;
	DefaultType	fSUM18, fSUMP35, fZOH27, fC_BUFFER_13, fFCNM9, fUDELAY11, fMULT4;
	DefaultType	fUDELAY10, fSUM16, fSUMP31, fZOH26, fC_BUFFER_11, fFCNM2, fUDELAY9;
	DefaultType	fMULT2, fUDELAY5, fSUM5, fSUMP6, fZOH25, fC_BUFFER_12, fFCNM1;
	DefaultType	fSUMP28, fP52, fSUMP27, fP51, fSUMP9, fP49, fSUMP39, fMAX_MIN2;
	DefaultType	fMAX_MIN4, fMAX_MIN1, fMAX_MIN3, fP50, fDQO1_2, fDQO1_1, fDQO1;
	DefaultType	fLIM29, fSUM11, fP32, fLIM8, fSUMP18, fP23, fLIM27, fSUMP16;
	DefaultType	fLIM26, fSUMP17, fP22, fP21, fSUM10, fLIM18, fSUMP24, fP29;
	DefaultType	fLIM24, fSUMP15, fLIM23, fSUMP13, fLIM22, fSUMP14, fP20, fP19;
	DefaultType	fSUM9, fSUMP22, fLIM19, fSUMP23, fP28, fP27, fSUM13, fABC4_2;
	DefaultType	fABC4_1, fABC4, fSUM22, fZOH21, fSUMP57, fPSM_F28004x_ADC1_1;
	DefaultType	fSUM21, fC11, fZOH36, fSUMP59, fPSM_F28004x_ADC1_3, fLIM20;
	DefaultType	fSUMP21, fABC3_2, fABC3_1, fABC3, fSUM20, fSUM14, fC10, fZOH19;
	DefaultType	fSUMP55, fPSM_F28004x_ADC2_4, fZOH18, fSUMP54, fPSM_F28004x_ADC1_8;
	DefaultType	fLIM25, fSUMP25, fP31, fSUMP19, fLIM21, fSUMP20, fP26, fP25;
	DefaultType	fSUM12, fABC1_2, fABC1_1, fABC1, fFCNM5, fFCNM4, fZOH9, fSUMP52;
	DefaultType	fPSM_F28004x_ADC1, fFCNM3, fZOH8, fSUMP51, fPSM_F28004x_ADC1_4;
	DefaultType	fZOH10, fSUMP53, fVDC14, fPSM_F28004x_ADC1_2, fABC5_2, fABC5_1;
	DefaultType	fABC5, fP16, fFCNM12, fFCNM11, fMULT5, fP41, fSUMP32, fVDC8;
	DefaultType	fSUMP34, fLIM31, fSUMP33, fFCNM10, fMULT7, fP45, fSUMP36, fVDC10;
	DefaultType	fSUMP38, fLIM32, fSUMP37, fMULT3, fSSCB1_3, fSSCB1_2, fSSCB1_1;
	DefaultType	fSSCB1, fP37, fI_RESET_I_D1, fMULT8, fF004x_DIN1, fVDC2, fP38;
	DefaultType	fSUMP2, fVDC3, fSUMP30, fLIM30, fSUMP29;

	ADC_CLR(2) = 1 << (1-1);	// Clear ADC interrupt flag 1
	CPU_PIEACK |= M__INT1;
	fC_BUFFER_12 = aryC_BUFFER_12[nGblC_BUFFER_12];

	fUDELAY5 = fGblUDELAY5;

	fUDELAY9 = fGblUDELAY9;

	fC_BUFFER_11 = aryC_BUFFER_11[nGblC_BUFFER_11];

	fUDELAY10 = fGblUDELAY10;

	fUDELAY11 = fGblUDELAY11;

	fC_BUFFER_13 = aryC_BUFFER_13[nGblC_BUFFER_13];

	fUDELAY12 = fGblUDELAY12;

	fUDELAY13 = fGblUDELAY13;


	fSUMP29 = fGblP36 + fUDELAY9;
	fLIM30 = (fSUMP29 > 0.15) ? 0.15 : ((fSUMP29 < (-0.15)) ? (-0.15) : fSUMP29);
	fSUMP30 = fGblP35 + fLIM30;
	fVDC3 = 0.5;
	fSUMP2 = fSUMP30 + fVDC3;
	fP38 = fSUMP2 * 1.414;
	fVDC2 = 20000;
	fF004x_DIN1 = PSM_GpioGetInput(10);
	fMULT8 = fVDC2 * fF004x_DIN1;
	{
		static DefaultType out_A = 0;
		static DefaultType in_A = 0.0;
		fI_RESET_I_D1 = out_A + 0.5/20000 * (fMULT8 + in_A);
		if (fI_RESET_I_D1 > 300) {
			fI_RESET_I_D1 -= 300 - 0;
		} else if (fI_RESET_I_D1 < 0) {
			fI_RESET_I_D1 += 300 - 0;
		}
		out_A = fI_RESET_I_D1; in_A = fMULT8;
	}
	fP37 = fI_RESET_I_D1 * (4.0/3.0);
	{
		static double SineTable1[400]={
		0.015707354,
		0.031410833,
		0.047106561,
		0.062790666,
		0.078459279,
		0.094108533,
		0.109734567,
		0.125333525,
		0.140901559,
		0.156434828,
		0.171929498,
		0.187381748,
		0.202787763,
		0.218143743,
		0.2334459,
		0.248690456,
		0.263873652,
		0.278991741,
		0.294040992,
		0.309017693,
		0.323918148,
		0.338738681,
		0.353475634,
		0.368125372,
		0.382684281,
		0.397148767,
		0.411515263,
		0.425780222,
		0.439940126,
		0.453991482,
		0.467930821,
		0.481754704,
		0.495459721,
		0.509042491,
		0.522499661,
		0.535827911,
		0.549023954,
		0.562084532,
		0.575006424,
		0.587786441,
		0.60042143,
		0.612908273,
		0.625243889,
		0.637425235,
		0.649449305,
		0.661313133,
		0.67301379,
		0.684548391,
		0.695914089,
		0.70710808,
		0.718127601,
		0.728969935,
		0.739632405,
		0.750112381,
		0.760407278,
		0.770514554,
		0.780431716,
		0.790156318,
		0.79968596,
		0.80901829,
		0.818151006,
		0.827081854,
		0.835808632,
		0.844329185,
		0.852641412,
		0.860743261,
		0.868632734,
		0.876307883,
		0.883766816,
		0.891007692,
		0.898028723,
		0.904828179,
		0.91140438,
		0.917755705,
		0.923880587,
		0.929777514,
		0.935445031,
		0.940881739,
		0.946086299,
		0.951057424,
		0.95579389,
		0.960294526,
		0.964558223,
		0.968583928,
		0.972370649,
		0.975917451,
		0.979223459,
		0.982287856,
		0.985109888,
		0.987688858,
		0.990024129,
		0.992115125,
		0.99396133,
		0.99556229,
		0.996917608,
		0.99802695,
		0.998890043,
		0.999506673,
		0.99987669,
		1,
		0.999876574,
		0.999506443,
		0.998889697,
		0.998026489,
		0.996917031,
		0.995561598,
		0.993960524,
		0.992114204,
		0.990023094,
		0.987687709,
		0.985108625,
		0.98228648,
		0.979221969,
		0.975915848,
		0.972368934,
		0.968582101,
		0.964556284,
		0.960292476,
		0.95579173,
		0.951055154,
		0.946083919,
		0.940879251,
		0.935442434,
		0.929774809,
		0.923877775,
		0.917752788,
		0.911401357,
		0.904825051,
		0.898025491,
		0.891004356,
		0.883763378,
		0.876304344,
		0.868629094,
		0.860739521,
		0.852637573,
		0.844325249,
		0.835804599,
		0.827077725,
		0.818146782,
		0.809013972,
		0.799681549,
		0.790151815,
		0.780427123,
		0.770509871,
		0.760402507,
		0.750107523,
		0.739627461,
		0.728964906,
		0.718122489,
		0.707102885,
		0.695908813,
		0.684543036,
		0.673008357,
		0.661307622,
		0.649443719,
		0.637419575,
		0.625238156,
		0.612902468,
		0.600415555,
		0.587780498,
		0.575000414,
		0.562078456,
		0.549017814,
		0.535821709,
		0.522493397,
		0.509036167,
		0.49545334,
		0.481748266,
		0.467924328,
		0.453984936,
		0.439933529,
		0.425773575,
		0.411508567,
		0.397142025,
		0.382677494,
		0.368118542,
		0.353468762,
		0.338731768,
		0.323911198,
		0.309010706,
		0.294033971,
		0.278984686,
		0.263866566,
		0.248683341,
		0.233438756,
		0.218136574,
		0.202780569,
		0.187374531,
		0.171922261,
		0.156427572,
		0.140894286,
		0.125326237,
		0.109727265,
		0.094101219,
		0.078451955,
		0.062783334,
		0.047099223,
		0.03140349,
		0.015700009,
		-7.34641E-06,
		-0.0157147,
		-0.031418175,
		-0.047113899,
		-0.062797998,
		-0.078466603,
		-0.094115847,
		-0.109741869,
		-0.125340814,
		-0.140908832,
		-0.156442084,
		-0.171936735,
		-0.187388964,
		-0.202794957,
		-0.218150913,
		-0.233453043,
		-0.248697572,
		-0.263880738,
		-0.278998796,
		-0.294048014,
		-0.30902468,
		-0.323925098,
		-0.338745593,
		-0.353482506,
		-0.368132203,
		-0.382691068,
		-0.397155509,
		-0.411521958,
		-0.425786869,
		-0.439946724,
		-0.453998027,
		-0.467937313,
		-0.481761142,
		-0.495466103,
		-0.509048814,
		-0.522505925,
		-0.535834114,
		-0.549030094,
		-0.562090608,
		-0.575012435,
		-0.587792384,
		-0.600427304,
		-0.612914077,
		-0.625249622,
		-0.637430896,
		-0.649454891,
		-0.661318643,
		-0.673019224,
		-0.684553746,
		-0.695919365,
		-0.707113275,
		-0.718132714,
		-0.728974964,
		-0.739637349,
		-0.75011724,
		-0.760412049,
		-0.770519237,
		-0.78043631,
		-0.790160821,
		-0.799690371,
		-0.809022608,
		-0.81815523,
		-0.827085984,
		-0.835812665,
		-0.844333122,
		-0.85264525,
		-0.860747001,
		-0.868636374,
		-0.876311422,
		-0.883770254,
		-0.891011027,
		-0.898031955,
		-0.904831306,
		-0.911407403,
		-0.917758623,
		-0.923883398,
		-0.929780218,
		-0.935447627,
		-0.940884228,
		-0.946088678,
		-0.951059694,
		-0.95579605,
		-0.960296576,
		-0.964560161,
		-0.968585755,
		-0.972372364,
		-0.975919054,
		-0.979224948,
		-0.982289233,
		-0.985111151,
		-0.987690007,
		-0.990025164,
		-0.992116046,
		-0.993962136,
		-0.995562981,
		-0.996918184,
		-0.998027411,
		-0.998890389,
		-0.999506904,
		-0.999876805,
		-1,
		-0.999876459,
		-0.999506212,
		-0.998889351,
		-0.998026027,
		-0.996916455,
		-0.995560907,
		-0.993959718,
		-0.992113283,
		-0.990022058,
		-0.987686559,
		-0.985107362,
		-0.982285103,
		-0.979220479,
		-0.975914246,
		-0.972367219,
		-0.968580274,
		-0.964554346,
		-0.960290427,
		-0.955789569,
		-0.951052884,
		-0.946081539,
		-0.940876762,
		-0.935439837,
		-0.929772105,
		-0.923874964,
		-0.91774987,
		-0.911398334,
		-0.904821923,
		-0.898022259,
		-0.891001021,
		-0.883759941,
		-0.876300805,
		-0.868625454,
		-0.860735782,
		-0.852633735,
		-0.844321312,
		-0.835800565,
		-0.827073596,
		-0.818142557,
		-0.809009654,
		-0.799677138,
		-0.790147313,
		-0.78042253,
		-0.770505188,
		-0.760397735,
		-0.750102665,
		-0.739622517,
		-0.728959877,
		-0.718117376,
		-0.70709769,
		-0.695903538,
		-0.684537681,
		-0.673002923,
		-0.661302111,
		-0.649438133,
		-0.637413914,
		-0.625232422,
		-0.612896663,
		-0.60040968,
		-0.587774554,
		-0.574994403,
		-0.56207238,
		-0.549011674,
		-0.535815506,
		-0.522487133,
		-0.509029844,
		-0.495446959,
		-0.481741829,
		-0.467917836,
		-0.45397839,
		-0.439926932,
		-0.425766928,
		-0.411501871,
		-0.397135283,
		-0.382670706,
		-0.368111711,
		-0.35346189,
		-0.338724856,
		-0.323904247,
		-0.309003719,
		-0.294026949,
		-0.278977632,
		-0.26385948,
		-0.248676225,
		-0.233431613,
		-0.218129404,
		-0.202773375,
		-0.187367315,
		-0.171915024,
		-0.156420316,
		-0.140887013,
		-0.125318948,
		-0.109719963,
		-0.094093905,
		-0.078444631,
		-0.062776002,
		-0.047091884,
		-0.031396147,
		-0.015692663,
		1.46928E-05
		};
		int n, u, v, w;
		n = fP37;
		u = n + 30;
		if (u>=399)
		{
		  u = (u - 399);
		}
		v = n - 90;
		if (v<= 0)
		{
		  v = (v + 399);
		}
		w = n + 150;
		if (w>=399)
		{
		  w = (w - 399);
		}
		fSSCB1 = SineTable1 [n];
		fSSCB1_1 = SineTable1 [u];
		fSSCB1_2 = SineTable1 [v];
		fSSCB1_3 = SineTable1 [w];
	}
#ifdef	_DEBUG
	fGblSa = fSSCB1;
#endif

#ifdef	_DEBUG
	fGblSab = fSSCB1_1;
#endif

#ifdef	_DEBUG
	fGblSbc = fSSCB1_2;
#endif

#ifdef	_DEBUG
	fGblSca = fSSCB1_3;
#endif

	fMULT3 = fP38 * fSSCB1_1;
	fSUMP37 = fGblP43 + fUDELAY13;
	fLIM32 = (fSUMP37 > 0.15) ? 0.15 : ((fSUMP37 < (-0.15)) ? (-0.15) : fSUMP37);
	fSUMP38 = fGblP42 + fLIM32;
	fVDC10 = 0.5;
	fSUMP36 = fSUMP38 + fVDC10;
	fP45 = fSUMP36 * 1.414;
	fMULT7 = fP45 * fSSCB1_3;
	fFCNM10 = (fMULT3-fMULT7)/3.0;
	fSUMP33 = fGblP40 + fUDELAY11;
	fLIM31 = (fSUMP33 > 0.15) ? 0.15 : ((fSUMP33 < (-0.15)) ? (-0.15) : fSUMP33);
	fSUMP34 = fGblP39 + fLIM31;
	fVDC8 = 0.5;
	fSUMP32 = fSUMP34 + fVDC8;
	fP41 = fSUMP32 * 1.414;
	fMULT5 = fP41 * fSSCB1_2;
	fFCNM11 = (fMULT5-fMULT3)/3.0;
	fFCNM12 = (fMULT7-fMULT5)/3.0;
	fP16 = fI_RESET_I_D1 * ((2.0*3.1415926)/300.0);
	// ABC to DQ transformation
	fABC5 = 2.0/3.0 * (cos(fP16) * fFCNM10 + cos(fP16-2*3.14159265/3) * fFCNM11 + cos(fP16+2*3.14159265/3) * fFCNM12);
	fABC5_1 = 2.0/3.0 * (sin(fP16) * fFCNM10 + sin(fP16-2*3.14159265/3) * fFCNM11 + sin(fP16+2*3.14159265/3) * fFCNM12);
	fABC5_2 = (fFCNM10 + fFCNM11 + fFCNM12) / 3.0;
#ifdef	_DEBUG
	fGblVodc = fABC5;
#endif

	fPSM_F28004x_ADC1_2 = ADC_RESULT(2, 2) * (1.0 * PSM_VRefHiC / 4096.0);
	fVDC14 = (-1.65);
	fSUMP53 = fPSM_F28004x_ADC1_2 + fVDC14;
	fZOH10 = fSUMP53;
	fPSM_F28004x_ADC1_4 = ADC_RESULT(2, 4) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP51 = fPSM_F28004x_ADC1_4 + fVDC14;
	fZOH8 = fSUMP51;
	fFCNM3 = (fZOH10-fZOH8)/3.0;
	fPSM_F28004x_ADC1 = ADC_RESULT(2, 0) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP52 = fPSM_F28004x_ADC1 + fVDC14;
	fZOH9 = fSUMP52;
	fFCNM4 = (fZOH9-fZOH10)/3.0;
	fFCNM5 = (fZOH8-fZOH9)/3.0;
	// ABC to DQ transformation
	fABC1 = 2.0/3.0 * (cos(fP16) * fFCNM3 + cos(fP16-2*3.14159265/3) * fFCNM4 + cos(fP16+2*3.14159265/3) * fFCNM5);
	fABC1_1 = 2.0/3.0 * (sin(fP16) * fFCNM3 + sin(fP16-2*3.14159265/3) * fFCNM4 + sin(fP16+2*3.14159265/3) * fFCNM5);
	fABC1_2 = (fFCNM3 + fFCNM4 + fFCNM5) / 3.0;
#ifdef	_DEBUG
	fGblVod = fABC1;
#endif

	fSUM12 = fABC5 - fABC1;
	fP25 = fSUM12 * 2;
	fP26 = fSUM12 * 0.01;
	fSUMP20 = fP26 + fGblUDELAY15;
	fLIM21 = (fSUMP20 > 1.5) ? 1.5 : ((fSUMP20 < (-1.5)) ? (-1.5) : fSUMP20);
	fSUMP19 = fP25 + fLIM21;
	fP31 = fABC5_1 * (((100.0*377.0)*(10.0E-6/3.0))*0.264);
	fSUMP25 = fSUMP19 + fP31;
	fLIM25 = (fSUMP25 > 1.5) ? 1.5 : ((fSUMP25 < (-1.5)) ? (-1.5) : fSUMP25);
	fPSM_F28004x_ADC1_8 = ADC_RESULT(2, 5) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP54 = fPSM_F28004x_ADC1_8 + fVDC14;
	fZOH18 = fSUMP54;
	fPSM_F28004x_ADC2_4 = ADC_RESULT(0, 0) * (1.0 * PSM_VRefHiA / 4096.0);
	fSUMP55 = fPSM_F28004x_ADC2_4 + fVDC14;
	fZOH19 = fSUMP55;
	fC10 = 0;
	fSUM14 = fC10 - fZOH18;
	fSUM20 = fSUM14 - fZOH19;
	// ABC to DQ transformation
	fABC3 = 2.0/3.0 * (cos(fP16) * fZOH18 + cos(fP16-2*3.14159265/3) * fZOH19 + cos(fP16+2*3.14159265/3) * fSUM20);
	fABC3_1 = 2.0/3.0 * (sin(fP16) * fZOH18 + sin(fP16-2*3.14159265/3) * fZOH19 + sin(fP16+2*3.14159265/3) * fSUM20);
	fABC3_2 = (fZOH18 + fZOH19 + fSUM20) / 3.0;
	fSUMP21 = fLIM25 + fABC3;
	fLIM20 = (fSUMP21 > 1.5) ? 1.5 : ((fSUMP21 < (-1.5)) ? (-1.5) : fSUMP21);
	fPSM_F28004x_ADC1_3 = ADC_RESULT(2, 3) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP59 = fPSM_F28004x_ADC1_3 + fVDC14;
	fZOH36 = fSUMP59;
	fC11 = 0;
	fSUM21 = fC11 - fZOH36;
	fPSM_F28004x_ADC1_1 = ADC_RESULT(2, 1) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP57 = fPSM_F28004x_ADC1_1 + fVDC14;
	fZOH21 = fSUMP57;
	fSUM22 = fSUM21 - fZOH21;
	// ABC to DQ transformation
	fABC4 = 2.0/3.0 * (cos(fP16) * fZOH36 + cos(fP16-2*3.14159265/3) * fSUM22 + cos(fP16+2*3.14159265/3) * fZOH21);
	fABC4_1 = 2.0/3.0 * (sin(fP16) * fZOH36 + sin(fP16-2*3.14159265/3) * fSUM22 + sin(fP16+2*3.14159265/3) * fZOH21);
	fABC4_2 = (fZOH36 + fSUM22 + fZOH21) / 3.0;
#ifdef	_DEBUG
	fGblIod = fABC4;
#endif

#ifdef	_DEBUG
	fGblIoq = fABC4_1;
#endif

	fSUM13 = fLIM20 - fABC4;
	fP27 = fSUM13;
	fP28 = fSUM13 * 0.33;
	fSUMP23 = fP28 + fGblUDELAY17;
	fLIM19 = (fSUMP23 > 5) ? 5 : ((fSUMP23 < (-5)) ? (-5) : fSUMP23);
	fSUMP22 = fP27 + fLIM19;
	fSUM9 = fABC5_1 - fABC1_1;
	fP19 = fSUM9 * 2;
	fP20 = fSUM9 * 0.01;
	fSUMP14 = fP20 + fGblUDELAY3;
	fLIM22 = (fSUMP14 > 1.5) ? 1.5 : ((fSUMP14 < (-1.5)) ? (-1.5) : fSUMP14);
	fSUMP13 = fP19 + fLIM22;
	fLIM23 = (fSUMP13 > 1.5) ? 1.5 : ((fSUMP13 < (-1.5)) ? (-1.5) : fSUMP13);
	fSUMP15 = fLIM23 + fABC3_1;
	fLIM24 = (fSUMP15 > 1.5) ? 1.5 : ((fSUMP15 < (-1.5)) ? (-1.5) : fSUMP15);
	fP29 = fLIM24 * (377.0*600.0E-6)/((940.0/28.0)*0.264);
	fSUMP24 = fSUMP22 + fP29;
	fLIM18 = (fSUMP24 > 5) ? 5 : ((fSUMP24 < (-5)) ? (-5) : fSUMP24);
	fSUM10 = fLIM24 - fABC4_1;
	fP21 = fSUM10;
	fP22 = fSUM10 * 0.33;
	fSUMP17 = fP22 + fGblUDELAY16;
	fLIM26 = (fSUMP17 > 5) ? 5 : ((fSUMP17 < (-5)) ? (-5) : fSUMP17);
	fSUMP16 = fP21 + fLIM26;
	fLIM27 = (fSUMP16 > 5) ? 5 : ((fSUMP16 < (-5)) ? (-5) : fSUMP16);
	fP23 = fABC5_1 * 10;
	fSUMP18 = fLIM27 + fP23;
	fLIM8 = (fSUMP18 > 5) ? 5 : ((fSUMP18 < (-5)) ? (-5) : fSUMP18);
	fP32 = fLIM20 * (377.0*600.0E-6)/((940.0/28.0)*0.264);
	fSUM11 = fLIM8 - fP32;
	fLIM29 = (fSUM11 > 5) ? 5 : ((fSUM11 < (-5)) ? (-5) : fSUM11);
	// DQ to ABC transformation
	fDQO1 = cos(fP16) * fLIM18 + sin(fP16) * fLIM29 + 0;
	fDQO1_1 = cos(fP16 - 2*3.14159265/3) * fLIM18 + sin(fP16 - 2*3.14159265/3) * fLIM29 + 0;
	fDQO1_2 = cos(fP16 + 2*3.14159265/3) * fLIM18 + sin(fP16 + 2*3.14159265/3) * fLIM29 + 0;
	fP50 = fDQO1;
fMAX_MIN3 = (fDQO1 < fDQO1_1) ? fDQO1_1 : fDQO1;
fMAX_MIN1 = (fMAX_MIN3 < fDQO1_2) ? fDQO1_2 : fMAX_MIN3;
fMAX_MIN4 = (fDQO1 > fDQO1_1) ? fDQO1_1 : fDQO1;
fMAX_MIN2 = (fDQO1_2 > fMAX_MIN4) ? fMAX_MIN4 : fDQO1_2;
	fSUMP39 = fMAX_MIN1 + fMAX_MIN2;
	fP49 = fSUMP39 * (-0.5);
	fSUMP9 = fP50 + fP49;
#ifdef	_DEBUG
	fGblVcona = fSUMP9;
#endif
	fP51 = fDQO1_1;
	fSUMP27 = fP51 + fP49;
#ifdef	_DEBUG
	fGblVconb = fSUMP27;
#endif
	fP52 = fDQO1_2;
	fSUMP28 = fP52 + fP49;
#ifdef	_DEBUG
	fGblVconc = fSUMP28;
#endif
#ifdef	_DEBUG
	fGblIodc = fLIM20;
#endif
#ifdef	_DEBUG
	fGblIoqc = fLIM24;
#endif
	fFCNM1 = (fZOH10*fZOH10)/150.0;
	aryC_BUFFER_12[nGblC_BUFFER_12] = fFCNM1; nGblC_BUFFER_12 = (nGblC_BUFFER_12 >= 150 - 1) ? 0 : (nGblC_BUFFER_12 + 1);
	fZOH25 = fFCNM1;
	fSUMP6 = fZOH25 + fUDELAY5;
	fSUM5 = fSUMP6 - fC_BUFFER_12;
	fGblUDELAY5 = fSUM5;
	fGblSQ2 = sqrt(fSUM5);
	fMULT2 = fLIM30 * fF004x_DIN1;
	fGblUDELAY9 = fMULT2;
	fFCNM2 = (fZOH9*fZOH9)/150.0;
	aryC_BUFFER_11[nGblC_BUFFER_11] = fFCNM2; nGblC_BUFFER_11 = (nGblC_BUFFER_11 >= 150 - 1) ? 0 : (nGblC_BUFFER_11 + 1);
	fZOH26 = fFCNM2;
	fSUMP31 = fZOH26 + fUDELAY10;
	fSUM16 = fSUMP31 - fC_BUFFER_11;
	fGblUDELAY10 = fSUM16;
	fGblSQ1 = sqrt(fSUM16);
	fMULT4 = fLIM31 * fF004x_DIN1;
	fGblUDELAY11 = fMULT4;
	fFCNM9 = (fZOH8*fZOH8)/150.0;
	aryC_BUFFER_13[nGblC_BUFFER_13] = fFCNM9; nGblC_BUFFER_13 = (nGblC_BUFFER_13 >= 150 - 1) ? 0 : (nGblC_BUFFER_13 + 1);
	fZOH27 = fFCNM9;
	fSUMP35 = fZOH27 + fUDELAY12;
	fSUM18 = fSUMP35 - fC_BUFFER_13;
	fGblUDELAY12 = fSUM18;
	fGblSQ3 = sqrt(fSUM18);
	fMULT6 = fLIM32 * fF004x_DIN1;
	fGblUDELAY13 = fMULT6;
	fGblMULT1 = fLIM21 * fF004x_DIN1;
	fGblMULT9 = fLIM22 * fF004x_DIN1;
	fGblMULT10 = fLIM26 * fF004x_DIN1;
	fGblMULT11 = fLIM19 * fF004x_DIN1;
	fZOH37 = fZOH10;
	fZOH38 = fZOH9;
	fZOH39 = fZOH8;
	fZOH40 = fFCNM3;
	fZOH41 = fFCNM4;
	fZOH42 = fFCNM5;
	fZOH43 = fZOH18;
	fZOH44 = fZOH19;
	fZOH46 = fZOH36;
	fZOH48 = fZOH21;
	fZOH14 = fABC4;
	fZOH15 = fLIM20;
	fZOH16 = fABC4_1;
	fZOH17 = fLIM24;
	fZOH23 = fABC1_1;
	fZOH24 = fABC5_1;
	fZOH28 = fABC1;
	fZOH29 = fABC5;
	fZOH30 = fGblP46;
	fZOH31 = fGblP47;
	fZOH32 = fGblP48;
	fZOH33 = fSUMP9;
	fZOH34 = fSUMP27;
	fZOH35 = fSUMP28;
	fLIM15 = (fSUMP9 > 4.5) ? 4.5 : ((fSUMP9 < (-4.5)) ? (-4.5) : fSUMP9);
	fLIM16 = (fSUMP27 > 4.5) ? 4.5 : ((fSUMP27 < (-4.5)) ? (-4.5) : fSUMP27);
	fLIM17 = (fSUMP28 > 4.5) ? 4.5 : ((fSUMP28 < (-4.5)) ? (-4.5) : fSUMP28);
	if (fF004x_DIN1 > 0.3)
	{
		PSM_Pwm3phStart(101-100);
	}
	fNOT1 = (fF004x_DIN1 <= 0.3) ? 1 : 0;
	if (fNOT1 > 0.3)
	{
		PSM_Pwm3phStop(101-100);
	}
	// Start of changing PWM3ph1 registers
	// Set Duty Cycle of U
	PWM_CMPA(1) = PWM_TBPRD(1) * (__fsat(fLIM15, 10 + (-5), (-5)) - (-5)) * (1.0/10);
	PWM_CMPA(2) = PWM_TBPRD(1) * (__fsat(fLIM16, 10 + (-5), (-5)) - (-5)) * (1.0/10);
	PWM_CMPA(3) = PWM_TBPRD(1) * (__fsat(fLIM17, 10 + (-5), (-5)) - (-5)) * (1.0/10);
	// End of changing PWM3ph1 registers
	if (nGblSciState != PSC_SCI_INITIAL) {
		_ProcSciOutput(0, fZOH37);
		_ProcSciOutput(1, fZOH38);
		_ProcSciOutput(2, fZOH39);
		_ProcSciOutput(3, fZOH40);
		_ProcSciOutput(4, fZOH41);
		_ProcSciOutput(5, fZOH42);
		_ProcSciOutput(6, fZOH43);
		_ProcSciOutput(7, fZOH44);
		_ProcSciOutput(8, fZOH46);
		_ProcSciOutput(9, fZOH48);
		_ProcSciOutput(10, fZOH14);
		_ProcSciOutput(11, fZOH15);
		_ProcSciOutput(12, fZOH16);
		_ProcSciOutput(13, fZOH17);
		_ProcSciOutput(14, fZOH23);
		_ProcSciOutput(15, fZOH24);
		_ProcSciOutput(16, fZOH28);
		_ProcSciOutput(17, fZOH29);
		_ProcSciOutput(18, fZOH30);
		_ProcSciOutput(19, fZOH31);
		_ProcSciOutput(20, fZOH32);
		_ProcSciOutput(21, fZOH33);
		_ProcSciOutput(22, fZOH34);
		_ProcSciOutput(23, fZOH35);
	}
}

interrupt void Task_1()
{
	DefaultType	fUDELAY17, fUDELAY16, fUDELAY3, fUDELAY15, fSUM19, fTF_D3, fVDC11;
	DefaultType	fSUM17, fTF_D2, fVDC9, fSUM15, fTF_D1, fVDC7;

	PSM_Timer1IntrEntry();
	fUDELAY15 = fGblUDELAY15;

	fUDELAY3 = fGblUDELAY3;

	fUDELAY16 = fGblUDELAY16;

	fUDELAY17 = fGblUDELAY17;


	fVDC7 = 0.32;
	{
		int i = 2 + 1;
		static DefaultType fIn[2 + 1] = {0, 
			0, 0};
		static DefaultType fOut[2 + 1] = {0, 
			0, 0};
		const DefaultType aAry[2 + 1] = {1,
			-1.9902253,
			0.99027381};
		const DefaultType bAry[2 + 1] = {1.2125294E-005,
			2.4250587E-005,
			1.2125294E-005};
		fTF_D1 = bAry[0] * fGblSQ2;
		while (--i > 0)
		{
			fIn[i] = fIn[i - 1];
			fOut[i] = fOut[i - 1];
			fTF_D1 += bAry[i] * fIn[i] - aAry[i] * fOut[i];
		}
		fOut[0] = fTF_D1;
		fIn[0] = fGblSQ2;
	}
	fSUM15 = fVDC7 - fTF_D1;
	fGblP35 = fSUM15;
	fGblP36 = fSUM15 * 0.002;
	fVDC9 = 0.32;
	{
		int i = 2 + 1;
		static DefaultType fIn[2 + 1] = {0, 
			0, 0};
		static DefaultType fOut[2 + 1] = {0, 
			0, 0};
		const DefaultType aAry[2 + 1] = {1,
			-1.9902253,
			0.99027381};
		const DefaultType bAry[2 + 1] = {1.2125294E-005,
			2.4250587E-005,
			1.2125294E-005};
		fTF_D2 = bAry[0] * fGblSQ1;
		while (--i > 0)
		{
			fIn[i] = fIn[i - 1];
			fOut[i] = fOut[i - 1];
			fTF_D2 += bAry[i] * fIn[i] - aAry[i] * fOut[i];
		}
		fOut[0] = fTF_D2;
		fIn[0] = fGblSQ1;
	}
	fSUM17 = fVDC9 - fTF_D2;
	fGblP39 = fSUM17;
	fGblP40 = fSUM17 * 0.002;
	fVDC11 = 0.32;
	{
		int i = 2 + 1;
		static DefaultType fIn[2 + 1] = {0, 
			0, 0};
		static DefaultType fOut[2 + 1] = {0, 
			0, 0};
		const DefaultType aAry[2 + 1] = {1,
			-1.9902253,
			0.99027381};
		const DefaultType bAry[2 + 1] = {1.2125294E-005,
			2.4250587E-005,
			1.2125294E-005};
		fTF_D3 = bAry[0] * fGblSQ3;
		while (--i > 0)
		{
			fIn[i] = fIn[i - 1];
			fOut[i] = fOut[i - 1];
			fTF_D3 += bAry[i] * fIn[i] - aAry[i] * fOut[i];
		}
		fOut[0] = fTF_D3;
		fIn[0] = fGblSQ3;
	}
	fSUM19 = fVDC11 - fTF_D3;
	fGblP42 = fSUM19;
	fGblP43 = fSUM19 * 0.002;
	fGblP46 = fTF_D1 * 100;
	fGblP47 = fTF_D2 * 100;
	fGblP48 = fTF_D3 * 100;
#ifdef	_DEBUG
	fGblV1rms = fGblP46;
#endif
#ifdef	_DEBUG
	fGblV2rms = fGblP47;
#endif
#ifdef	_DEBUG
	fGblV3rms = fGblP48;
#endif
	fGblUDELAY15 = fGblMULT1;
	fGblUDELAY3 = fGblMULT9;
	fGblUDELAY16 = fGblMULT10;
	fGblUDELAY17 = fGblMULT11;
}


void Initialize(void)
{
	PS_SysInit(2, 20);
	PS_PwmStartStopClock(0);	// Stop Pwm Clock
	PS_TimerInit(0, 0);
	PS_SetVREF(0, 1, 0);	// Set external VRef for ADC-A
	PS_SetVREF(1, 1, 0);	// Set external VRef for ADC-B
	PS_SetVREF(2, 1, 0);	// Set external VRef for ADC-C

	{
	    int i, preAdcNo = -1;
	    /* PST_AdcAttr: Adc No., Channel No., Soc No., Trig Src, SampleTime(clock) */
	    const PST_AdcAttr aryAdcInit[7] = {
			{2, 0, 0, ADCTRIG_PWM1, 32},
			{2, 1, 1, ADCTRIG_PWM1, 32},
			{2, 2, 2, ADCTRIG_PWM1, 32},
			{2, 3, 3, ADCTRIG_PWM1, 32},
			{2, 4, 4, ADCTRIG_PWM1, 32},
			{2, 8, 5, ADCTRIG_PWM1, 32},
			{0, 4, 0, ADCTRIG_PWM1, 32}};
	    const PST_AdcAttr *p = aryAdcInit;
	    for (i = 0; i < 7; i++, p++) {
	        if (preAdcNo != p->nAdcNo) {
	            PS_AdcInit(p->nAdcNo);
	            preAdcNo = p->nAdcNo;
	        }
	        PS_AdcSetChn(p->nAdcNo, p->nChnNo, p->nSocNo, p->nTrigSrc, p->nWindSz);
	    }
	}

	PS_Pwm3phInit(1, 0, 1, 1.e6/(20000*1.0), ePwmStartHigh1, ePwmComplement, HRPWM_DISABLE);	// pwmNo, seqNo, wave type, period, PwmA, PWMB, UseHRPwm
	PS_PwmSetDeadBand(1, 0, 2, 3, 0, 2, 2);
	PS_PwmSetDeadBand(2, 0, 2, 3, 0, 2, 2);
	PS_PwmSetDeadBand(3, 0, 2, 3, 0, 2, 2);
	PS_PwmSetIntrType(1, ePwmIntrAdc, 1, 0);
	PS_AdcSetIntr(2, 1, 5, Task); // AdcNo, IntrNo, SocNo, Interrupt Vector
	PS_PwmSetTripAction(1, eTzHiZ, eTzHiZ);
	PS_PwmSetTripAction(2, eTzHiZ, eTzHiZ);
	PS_PwmSetTripAction(3, eTzHiZ, eTzHiZ);
	PWM_CMPA(1) = (0 - (-5)) / (1.0 * 10) * PWM_TBPRD(1);
	PWM_CMPA(2) = (0 - (-5)) / (1.0 * 10) * PWM_TBPRD(1);
	PWM_CMPA(3) = (0 - (-5)) / (1.0 * 10) * PWM_TBPRD(1);
	PSM_Pwm3phStop(1);       // Stop Pwm3ph1 at the beginning

	PS_GpioSetFunc(10, 0, eSync1Samp, eGpioIn, 0);

	PS_SciInit(13, 12, 115200, 0, aGblSciOutBuf, 64, &_ProcSciInputItem);	// Rx(GPIO13), Tx(GPIO12)

	PS_TimerInit(1, 5556L);
	PS_TimerSetIntrVector(1, Task_1);	// set timer1 interrupt vector
	PS_PwmStartStopClock(2);	// Start Pwm Clock, start Timer1
}


void main()
{
	Initialize();
	PSM_EnableIntr();   // Enable Global interrupt INTM
	PSM_EnableDbgm();
	for (;;) {
		_ProcSciWaitStart();
	}
}

